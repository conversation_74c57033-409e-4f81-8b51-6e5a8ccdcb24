# Google Gemini API 设置指南

## 概述

本项目现在使用 Google 官方的 Gemini API 来进行 AI 增强的产品信息提取。这替代了之前的 OpenRouter 方案，提供更稳定和直接的 API 访问。

## 获取 Gemini API Key

### 1. 访问 Google AI Studio

前往 [Google AI Studio](https://aistudio.google.com/) 并登录你的 Google 账户。

### 2. 创建 API Key

1. 在 Google AI Studio 中，点击左侧菜单的 "Get API key"
2. 点击 "Create API key" 按钮
3. 选择一个现有的 Google Cloud 项目，或创建一个新项目
4. 复制生成的 API key

### 3. 配置环境变量

在项目根目录的 `.env.local` 文件中添加你的 API key：

```bash
# Google Gemini API Key (for OG + Gemini parser)
GEMINI_API_KEY=your_actual_api_key_here
```

## API 使用说明

### 模型信息

- **模型名称**: `gemini-2.0-flash`
- **API 端点**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`
- **认证方式**: API Key 作为查询参数

### 请求格式

```bash
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=YOUR_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Your prompt here"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.1,
      "maxOutputTokens": 1000
    }
  }'
```

## 解析器架构

### OG + Gemini 解析器特点

1. **双重提取**: 
   - Open Graph Scraper 提取基础元数据
   - Google Gemini AI 进行智能分析和优化

2. **智能回退**:
   - 如果 OG 提取失败，AI 仍可基于 URL 生成信息
   - 如果 AI 分析失败，回退到基础 OG 数据

3. **高质量输出**:
   - 清理和标准化产品名称
   - 生成吸引人的标语
   - 提供准确的产品描述

### 解析流程

```
URL 输入 → Open Graph 提取 → Gemini AI 增强 → 结构化输出
    ↓              ↓                ↓
  验证URL      提取元数据        智能分析
    ↓              ↓                ↓
  标准化      获取图片URL       生成标语
    ↓              ↓                ↓
  错误处理    处理favicon       清理描述
```

## 测试

### 使用测试脚本

```bash
# 启动开发服务器
pnpm dev

# 在另一个终端运行测试
node test-gemini-parser.js https://example.com
```

### 预期输出

```
🚀 Testing OG + Gemini Parser via API
URL: https://example.com
==================================================
🔄 Sending request...
✅ Parsing successful!

📊 Extracted Information:
Name: Example
Tagline: A great example website
Description: This is an example website for demonstration purposes.
Logo URL: https://example.com/favicon.ico
Cover Image URL: https://example.com/og-image.png
```

## 故障排除

### 常见问题

1. **API Key 无效**
   - 确保在 Google AI Studio 中正确生成了 API key
   - 检查 `.env.local` 文件中的配置

2. **配额限制**
   - Gemini API 有免费配额限制
   - 查看 [Google AI Studio](https://aistudio.google.com/) 中的使用情况

3. **网络错误**
   - 确保网络连接正常
   - 检查防火墙设置

### 调试技巧

- 查看浏览器控制台日志
- 检查开发服务器的输出
- 使用 `curl` 命令直接测试 API

## 迁移说明

### 从 OpenRouter 迁移

如果你之前使用的是 OpenRouter 方案：

1. 获取 Gemini API key 并配置环境变量
2. 代码会自动使用新的 Gemini 解析器
3. 旧的 OpenRouter 配置可以保留作为备用

### 性能对比

- **Gemini**: 更快的响应时间，更稳定的服务
- **OpenRouter**: 可能有速率限制，需要处理多个模型

## 相关文档

- [Google AI Studio](https://aistudio.google.com/)
- [Gemini API 文档](https://ai.google.dev/docs)
- [项目解析器文档](./AUTO_PARSER.md)
