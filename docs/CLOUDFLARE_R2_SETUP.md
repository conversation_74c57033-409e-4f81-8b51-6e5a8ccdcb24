# Cloudflare R2 设置指南

本项目已统一使用 Cloudflare R2 作为图片存储服务，替代了之前的 Supabase Storage。

## 为什么选择 Cloudflare R2？

### 优势
- ✅ **免费额度丰富**：10GB 存储 + 每月 1000万次读取请求
- ✅ **S3 兼容**：使用标准的 AWS SDK
- ✅ **全球 CDN**：自动分发到全球边缘节点
- ✅ **零出站费用**：不像 AWS S3 那样收取流量费
- ✅ **高性能**：低延迟访问
- ✅ **可靠性**：99.9% 可用性保证

### 与 Supabase Storage 对比
| 特性 | Cloudflare R2 | Supabase Storage |
|------|---------------|------------------|
| 免费存储 | 10GB | 1GB |
| 免费请求 | 1000万/月 | 有限制 |
| 出站流量费 | 免费 | 收费 |
| 全球 CDN | 内置 | 需额外配置 |
| 性能 | 更快 | 较慢 |

## 设置步骤

### 1. 创建 Cloudflare 账户
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 注册或登录账户

### 2. 创建 R2 存储桶
1. 在 Cloudflare Dashboard 中，点击 **R2 Object Storage**
2. 点击 **Create bucket**
3. 输入存储桶名称（例如：`introducing-day-images`）
4. 选择区域（推荐：自动）
5. 点击 **Create bucket**

### 3. 获取 API 凭证
1. 在 R2 页面，点击 **Manage R2 API tokens**
2. 点击 **Create API token**
3. 配置权限：
   - **Token name**: `introducing-day-api`
   - **Permissions**: `Object Read & Write`
   - **Specify bucket**: 选择你创建的存储桶
4. 点击 **Create API token**
5. 保存以下信息：
   - Access Key ID
   - Secret Access Key
   - Account ID（在 R2 概览页面可以找到）

### 4. 配置公共访问
1. 在存储桶设置中，点击 **Settings**
2. 在 **Public access** 部分，点击 **Allow Access**
3. 记录公共 URL（格式：`https://pub-xxxxxx.r2.dev`）

### 5. 配置环境变量
在项目的 `.env.local` 文件中添加以下配置：

```bash
# Cloudflare R2 Configuration
CLOUDFLARE_R2_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key_here
CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name_here
CLOUDFLARE_R2_PUBLIC_URL=https://pub-xxxxxx.r2.dev
```

## 项目集成

### 已实现的功能
1. **R2 客户端** (`lib/cloudflare/r2-client.ts`)
   - 文件上传
   - 文件删除
   - 预签名 URL 生成
   - 唯一文件名生成

2. **上传 API** (`app/api/upload/r2/route.ts`)
   - 支持图片上传
   - 文件类型验证
   - 文件大小限制（1MB）
   - 自动文件夹分类

3. **上传组件** (`components/admin/r2-image-upload.tsx`)
   - 拖拽上传
   - 预览功能
   - 进度显示
   - 错误处理

4. **产品表单集成**
   - Logo 上传（存储在 `logos/` 文件夹）
   - 封面图片上传（存储在 `covers/` 文件夹）

### 文件夹结构
```
R2 存储桶/
├── logos/          # 产品 Logo
├── covers/         # 封面图片
├── screenshots/    # 产品截图
├── favicons/       # 网站图标
└── uploads/        # 其他上传文件
```

## 使用方法

### 在管理员界面上传图片
1. 进入产品编辑页面
2. 使用新的图片上传组件
3. 拖拽或点击上传图片
4. 系统自动上传到 R2 并返回 URL

### 在代码中使用 R2 客户端
```typescript
import { uploadToR2, deleteFromR2 } from '@/lib/cloudflare/r2-client'

// 上传文件
const url = await uploadToR2(fileBuffer, 'logos/my-logo.png', 'image/png')

// 删除文件
await deleteFromR2('logos/my-logo.png')
```

## 迁移说明

### 现有数据
- 项目中已有 60+ 个产品使用 R2 存储的图片
- 所有现有图片 URL 都指向 `pub-5b151ccdf8cd440da7fdcb9ac98c7e70.r2.dev`
- 无需迁移现有数据

### 新上传
- 所有新的图片上传都将使用 R2
- 管理员界面已更新为使用 R2 上传组件
- 自动生成唯一文件名避免冲突

## 故障排除

### 常见问题
1. **上传失败**
   - 检查环境变量是否正确配置
   - 确认 API 凭证有效
   - 检查文件大小是否超过限制

2. **图片无法显示**
   - 确认存储桶公共访问已启用
   - 检查 Next.js 图片域名配置
   - 验证图片 URL 格式

3. **权限错误**
   - 确认 API token 权限正确
   - 检查存储桶访问权限

### 调试技巧
- 查看浏览器控制台错误
- 检查网络请求状态
- 验证环境变量加载

## 性能优化

### 已实现
- 自动文件名生成（避免缓存问题）
- 文件夹分类（便于管理）
- 文件类型验证（安全性）
- 大小限制（性能）

### 可选优化
- 启用 Next.js 图片优化
- 配置 CDN 缓存策略
- 实现图片压缩
- 添加图片格式转换

## 安全考虑

- API 凭证存储在环境变量中
- 文件类型严格验证
- 文件大小限制
- 唯一文件名防止覆盖
- 服务器端上传（避免客户端暴露凭证）
