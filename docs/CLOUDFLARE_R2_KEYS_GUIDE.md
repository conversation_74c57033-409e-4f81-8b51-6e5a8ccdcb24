# 🔑 Cloudflare R2 密钥获取完整指南

本指南将详细说明如何获取 Cloudflare R2 所需的所有密钥和配置信息。

## 📋 需要获取的信息

你需要获取以下 5 个配置项：

1. **CLOUDFLARE_R2_ACCOUNT_ID** - Cloudflare 账户 ID
2. **CLOUDFLARE_R2_ACCESS_KEY_ID** - R2 API 访问密钥 ID
3. **CLOUDFLARE_R2_SECRET_ACCESS_KEY** - R2 API 秘密访问密钥
4. **CLOUDFLARE_R2_BUCKET_NAME** - R2 存储桶名称
5. **CLOUDFLARE_R2_PUBLIC_URL** - R2 公共访问 URL

## 🚀 步骤 1：创建 Cloudflare 账户

如果你还没有 Cloudflare 账户：

1. 访问 [Cloudflare 官网](https://www.cloudflare.com/)
2. 点击 **Sign Up** 注册账户
3. 验证邮箱并完成注册

## 📦 步骤 2：创建 R2 存储桶

### 2.1 进入 R2 控制台
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 在左侧菜单中找到 **R2 Object Storage**
3. 如果是第一次使用，点击 **Purchase R2** 开始使用（免费计划）

### 2.2 创建存储桶
1. 点击 **Create bucket** 按钮
2. 输入存储桶名称，例如：`introducing-day-images`
3. 选择位置：**Automatic**（推荐）
4. 点击 **Create bucket**

### 2.3 配置公共访问
1. 进入刚创建的存储桶
2. 点击 **Settings** 标签
3. 在 **Public access** 部分，点击 **Allow Access**
4. 确认启用公共访问
5. 记录显示的公共 URL，格式类似：`https://pub-xxxxxx.r2.dev`

## 🔑 步骤 3：获取 Account ID

### 方法 1：从 R2 页面获取
1. 在 R2 Object Storage 页面
2. 右侧会显示 **Account ID**
3. 复制这个 ID

### 方法 2：从账户设置获取
1. 在 Cloudflare Dashboard 右上角点击头像
2. 选择 **My Profile**
3. 在右侧找到 **Account ID**
4. 复制这个 ID

## 🗝️ 步骤 4：创建 R2 API Token

### 4.1 进入 API Token 页面
1. 在 Cloudflare Dashboard 中，点击右上角头像
2. 选择 **My Profile**
3. 点击 **API Tokens** 标签
4. 在 **R2 API tokens** 部分，点击 **Create API token**

### 4.2 配置 API Token
1. **Token name**: 输入描述性名称，如 `introducing-day-r2-api`
2. **Permissions**: 选择 **Object Read & Write**
3. **Specify bucket**: 选择你刚创建的存储桶
4. **TTL**: 可以选择永不过期或设置过期时间
5. 点击 **Create API token**

### 4.3 保存密钥信息
创建成功后，你会看到：
- **Access Key ID**: 类似 `f1234567890abcdef1234567890abcdef`
- **Secret Access Key**: 类似 `abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890`

⚠️ **重要**: Secret Access Key 只会显示一次，请立即复制并保存！

## 📝 步骤 5：配置环境变量

在你的项目根目录创建或编辑 `.env.local` 文件：

```bash
# Cloudflare R2 Configuration
CLOUDFLARE_R2_ACCOUNT_ID=你的账户ID
CLOUDFLARE_R2_ACCESS_KEY_ID=你的访问密钥ID
CLOUDFLARE_R2_SECRET_ACCESS_KEY=你的秘密访问密钥
CLOUDFLARE_R2_BUCKET_NAME=你的存储桶名称
CLOUDFLARE_R2_PUBLIC_URL=https://pub-xxxxxx.r2.dev
```

### 示例配置
```bash
# Cloudflare R2 Configuration
CLOUDFLARE_R2_ACCOUNT_ID=f1234567890abcdef1234567890abcdef
CLOUDFLARE_R2_ACCESS_KEY_ID=a1b2c3d4e5f6g7h8i9j0
CLOUDFLARE_R2_SECRET_ACCESS_KEY=1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
CLOUDFLARE_R2_BUCKET_NAME=introducing-day-images
CLOUDFLARE_R2_PUBLIC_URL=https://pub-5b151ccdf8cd440da7fdcb9ac98c7e70.r2.dev
```

## ✅ 步骤 6：验证配置

### 6.1 重启开发服务器
```bash
npm run dev
# 或
pnpm dev
```

### 6.2 测试上传功能
1. 访问管理员页面：`http://localhost:3000/admin`
2. 尝试创建或编辑产品
3. 使用新的图片上传组件上传图片
4. 检查是否成功上传到 R2

## 🔍 常见问题排查

### 问题 1：Account ID 错误
**错误信息**: `Invalid account ID`
**解决方案**: 
- 确认 Account ID 是 32 位十六进制字符串
- 检查是否复制了完整的 ID

### 问题 2：API Token 权限不足
**错误信息**: `Access denied`
**解决方案**:
- 确认 API Token 有 **Object Read & Write** 权限
- 确认 Token 指定了正确的存储桶
- 检查 Token 是否已过期

### 问题 3：存储桶不存在
**错误信息**: `Bucket not found`
**解决方案**:
- 确认存储桶名称拼写正确
- 确认存储桶在正确的账户下

### 问题 4：公共访问未启用
**错误信息**: 图片上传成功但无法访问
**解决方案**:
- 在存储桶设置中启用公共访问
- 确认公共 URL 格式正确

## 🔒 安全建议

1. **不要在代码中硬编码密钥**
2. **使用 `.env.local` 文件存储敏感信息**
3. **将 `.env.local` 添加到 `.gitignore`**
4. **定期轮换 API Token**
5. **为不同环境使用不同的存储桶**

## 💰 费用说明

### 免费额度
- **存储**: 10 GB/月
- **Class A 操作** (写入): 100万次/月
- **Class B 操作** (读取): 1000万次/月
- **出站流量**: 免费

### 超出免费额度的费用
- **存储**: $0.015/GB/月
- **Class A 操作**: $4.50/百万次
- **Class B 操作**: $0.36/百万次

对于大多数小型到中型项目，免费额度完全够用！

## 📞 获取帮助

如果遇到问题：
1. 查看 [Cloudflare R2 文档](https://developers.cloudflare.com/r2/)
2. 检查 [Cloudflare 社区论坛](https://community.cloudflare.com/)
3. 联系 Cloudflare 支持团队
