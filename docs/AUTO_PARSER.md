# 简化解析器系统文档

## 概述

简化的解析器系统专注于核心功能，提供简单可靠的产品信息提取。使用 OG + Gemini 解析器，失败时回退到基础解析。

## 解析器类型

### 1. OG + Gemini Parser (主要)
- **特点**: Open Graph 数据提取 + Google Gemini AI 智能增强
- **API**: Google Gemini API (官方)
- **配置**: 需要 `GEMINI_API_KEY` 环境变量
- **优势**: 稳定、快速、高质量输出

### 2. 基础解析 (回退)
- **特点**: 无需API，基于URL生成基础信息
- **配置**: 无需配置，总是可用

## 解析策略

系统使用简单的回退策略：
1. **OG + Gemini** - 首先尝试，结合元数据提取和AI增强
2. **基础解析** - Gemini 失败时使用，确保总有结果

## 使用方法

### 基础使用（推荐）
```typescript
import { parseProductFromUrl } from '@/lib/submit/url-parser'

const productInfo = await parseProductFromUrl('https://example.com')
console.log(productInfo)
```

### 直接使用解析器
```typescript
import { simpleParser } from '@/lib/parsers'

const result = await simpleParser.parse('https://example.com')
console.log(result)
```

### 直接使用 Gemini 解析器
```typescript
import { OgGeminiParser } from '@/lib/parsers'

// 使用 OG + Gemini
const ogGeminiParser = new OgGeminiParser()
if (ogGeminiParser.isAvailable()) {
  const result = await ogGeminiParser.parse('https://example.com')
  if (result.success) {
    console.log(result.data)
  }
}
```

## 配置选项

### 环境变量
```bash
# API密钥
GEMINI_API_KEY=your_gemini_api_key        # Google Gemini API (必需)
```

如果不配置 Gemini API 密钥，系统会自动使用基础解析。

## 解析结果

### 返回数据结构
```typescript
interface ParsedProductInfo {
  name?: string           // 产品名称
  tagline?: string        // 产品标语
  description?: string    // 产品描述
  logoUrl?: string        // Logo URL
  coverImageUrl?: string  // 封面图片 URL
  category?: string       // 产品分类
  tags?: string[]         // 标签列表
}
```

## 工作流程

1. **URL 输入** → 用户输入产品URL
2. **OG + Gemini 解析** → 尝试使用 Open Graph + Gemini AI 提取信息
3. **基础解析** → 如果 Gemini 失败，生成基础信息
4. **返回结果** → 总是返回产品信息

## 文件结构

```
lib/parsers/
├── types.ts              # 类型定义
├── og-gemini-parser.ts   # OG + Gemini 解析器
├── simple-parser.ts      # 简化解析管理器
└── index.ts              # 导出文件

lib/submit/
└── url-parser.ts         # URL解析入口

docs/
├── AUTO_PARSER.md        # 解析器系统文档
└── GEMINI_SETUP.md       # Gemini API 设置指南
```

## 最佳实践

1. **API密钥管理**: 使用环境变量存储API密钥
2. **错误处理**: 系统自动处理解析失败的情况
3. **性能监控**: 查看控制台日志了解解析器使用情况

## 故障排除

### 常见问题
1. **Gemini API密钥无效**: 检查 `.env.local` 文件中的 `GEMINI_API_KEY` 配置
2. **解析失败**: 系统会自动回退到基础解析，总是有结果
3. **结果质量差**: 确保配置了有效的 Gemini API 密钥

### 调试技巧
- 查看浏览器控制台日志
- 检查网络请求是否成功
- 验证 Gemini API 密钥是否正确
- 参考 [Gemini API 设置指南](./GEMINI_SETUP.md)
