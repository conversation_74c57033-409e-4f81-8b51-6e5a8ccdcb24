# Open Graph Scraper + OpenRouter 解析器

## 概述

这是一个结合了 **Open Graph Scraper** 和 **OpenRouter 免费模型** 的智能解析器，提供快速、准确且成本效益高的产品信息提取。

## 工作原理

### 两步解析流程

1. **第一步：Open Graph Scraper**
   - 快速提取网站的结构化数据（Open Graph、Twitter Cards、基础 meta 标签）
   - 获取标题、描述、图片、网站信息等
   - 速度快，成本低，覆盖面广

2. **第二步：OpenRouter 免费模型**
   - 使用 `meta-llama/llama-3.2-3b-instruct:free` 模型
   - 对提取的信息进行智能分析和优化
   - 清理标题、生成更好的标语、分类产品等

## 优势

### ✅ **成本效益**
- Open Graph Scraper：完全免费
- OpenRouter：使用免费的 Llama 模型
- 总成本：几乎为零

### ✅ **速度快**
- OG 数据提取：通常 1-3 秒
- AI 分析：通常 2-5 秒
- 总时间：5-8 秒（比 Jina AI ReaderLM-v2 更快）

### ✅ **准确性高**
- 结构化数据提取：非常可靠
- AI 智能分析：提升信息质量
- 回退机制：确保总有结果

### ✅ **覆盖面广**
- 支持所有有 Open Graph 标签的网站
- 对于没有 OG 标签的网站也能提供基础信息
- 适用于各种类型的产品网站

## 配置

### 环境变量

```bash
# .env.local
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 获取 OpenRouter API Key

1. 访问 [OpenRouter.ai](https://openrouter.ai/)
2. 注册账户
3. 获取免费的 API key
4. 免费模型每月有一定的使用额度

## 使用方法

### 在解析器链中使用

解析器会自动按以下顺序尝试：

1. **Jina AI** (如果配置了 API key)
2. **OG + OpenRouter** (如果配置了 API key) ← 新增
3. **Firecrawl** (如果配置了 API key)
4. **基础解析** (总是可用)

### 直接使用

```typescript
import { OgOpenRouterParser } from '@/lib/parsers/og-openrouter-parser'

const parser = new OgOpenRouterParser()

if (parser.isAvailable()) {
  const result = await parser.parse('https://example.com')
  console.log(result.data)
}
```

### 测试脚本

```bash
node scripts/test-og-parser.js https://vercel.com
```

## 解析结果示例

### 输入
```
URL: https://vercel.com
```

### Open Graph 数据
```json
{
  "title": "Vercel: Build and deploy the best web experiences with the Frontend Cloud",
  "description": "Vercel's Frontend Cloud gives developers the frameworks, workflows, and infrastructure to build a faster, more personalized web.",
  "image": "https://vercel.com/og-image.png",
  "siteName": "Vercel"
}
```

### AI 优化后
```json
{
  "name": "Vercel",
  "tagline": "Build and deploy the best web experiences with the Frontend Cloud",
  "description": "Vercel's Frontend Cloud gives developers the frameworks, workflows, and infrastructure to build a faster, more personalized web.",
  "logoUrl": "https://vercel.com/favicon.ico",
  "coverImageUrl": "https://vercel.com/og-image.png"
}
```

## 技术细节

### 使用的技术栈

- **open-graph-scraper**: 提取 Open Graph 数据
- **OpenAI SDK**: 连接 OpenRouter API
- **meta-llama/llama-3.2-3b-instruct:free**: 免费的 Llama 模型

### 错误处理

- OG 提取失败：使用基础 URL 信息
- AI 分析失败：回退到 OG 数据处理
- 完全失败：返回基于 URL 的基础信息

### 性能优化

- 懒加载：只在需要时初始化 OpenAI 客户端
- 超时控制：OG 提取 10 秒超时
- 重试机制：OG 提取失败时重试 2 次

## 与其他解析器对比

| 解析器 | 速度 | 成本 | 准确性 | 覆盖面 |
|--------|------|------|--------|--------|
| **OG + OpenRouter** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Jina AI | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Firecrawl | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 基础解析 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ |

## 最佳实践

1. **配置 API Key**: 确保设置了 `OPENROUTER_API_KEY`
2. **监控使用量**: 关注 OpenRouter 的免费额度使用情况
3. **回退策略**: 保持其他解析器作为备选方案
4. **测试验证**: 使用测试脚本验证解析效果

## 故障排除

### 常见问题

**Q: 解析器不工作？**
A: 检查 `OPENROUTER_API_KEY` 是否正确设置

**Q: AI 分析失败？**
A: 检查网络连接和 OpenRouter 服务状态，解析器会自动回退到 OG 数据

**Q: 某些网站解析效果不好？**
A: 可能是网站缺少 Open Graph 标签，这种情况下其他解析器可能效果更好

### 调试

启用详细日志：
```typescript
console.log('OG data:', ogData)
console.log('AI result:', aiResult)
```
