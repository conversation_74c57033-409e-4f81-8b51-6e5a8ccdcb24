#!/usr/bin/env node

/**
 * 测试 Open Graph Scraper + OpenRouter 解析器
 * 通过 API 端点测试
 */

async function testParser() {
  const url = process.argv[2] || 'https://vercel.com'

  console.log('🚀 Testing OG + OpenRouter Parser via API')
  console.log('URL:', url)
  console.log('=' .repeat(50))

  try {
    console.log('🔄 Sending request...')

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const response = await fetch('http://localhost:3002/api/parse-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorData = await response.json()
      console.log('❌ API request failed:', errorData.error)
      return
    }

    const result = await response.json()

    if (result.success) {
      console.log('✅ Parsing successful!')
      console.log('')
      console.log('📊 Extracted Information:')
      console.log('Name:', result.data.name)
      console.log('Tagline:', result.data.tagline)
      console.log('Description:', result.data.description)
      console.log('Logo URL:', result.data.logoUrl)
      console.log('Cover Image URL:', result.data.coverImageUrl)
    } else {
      console.log('❌ Parsing failed:', result.error)
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('💥 Request timed out after 30 seconds')
    } else {
      console.error('💥 Error:', error.message)
    }
  }
}

testParser()
