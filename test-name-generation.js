#!/usr/bin/env node

/**
 * 测试改进后的名称生成功能
 */

function generateNameFromUrl(url) {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
    const domain = urlObj.hostname.replace('www.', '')
    const domainParts = domain.split('.')
    const domainName = domainParts[0]
    
    // 处理连字符分隔的域名
    if (domainName.includes('-')) {
      return domainName
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    }
    
    // 处理驼峰命名或单词组合（如 aiNamer, openAI 等）
    const camelCaseWords = domainName.replace(/([a-z])([A-Z])/g, '$1 $2')
    
    // 如果没有空格，可能是单个词，直接首字母大写
    if (!camelCaseWords.includes(' ')) {
      return domainName.charAt(0).toUpperCase() + domainName.slice(1)
    }
    
    // 处理每个词的首字母大写
    return camelCaseWords
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  } catch {
    return 'Unknown Product'
  }
}

// 测试用例
const testUrls = [
  'https://ai-namer.com',
  'https://openai.com',
  'https://vercel.com',
  'https://github.com',
  'https://linear.app',
  'https://figma.com',
  'https://stripe.com',
  'https://notion.so',
  'https://my-awesome-app.com',
  'https://aiNamer.com',
  'https://openAI.com',
  'https://superCoolApp.com'
]

console.log('🧪 Testing improved name generation:')
console.log('=' .repeat(50))

testUrls.forEach(url => {
  const name = generateNameFromUrl(url)
  console.log(`${url.padEnd(30)} → ${name}`)
})

console.log('=' .repeat(50))
console.log('✅ Test completed!')
