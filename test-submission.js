#!/usr/bin/env node

/**
 * 测试产品提交功能
 */

async function testSubmission() {
  console.log('🧪 Testing Product Submission API')
  console.log('=' .repeat(50))

  const testData = {
    url: 'https://example-product.com',
    name: 'Test Product',
    tagline: 'A great test product for testing',
    description: 'This is a test product submission to verify the database integration works correctly.',
    logoUrl: 'https://example-product.com/logo.png',
    coverImageUrl: 'https://example-product.com/cover.jpg',
    category: 'SaaS',
    tags: ['test', 'demo', 'api'],
    preferredDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
  }

  try {
    console.log('📤 Sending submission request...')

    const response = await fetch('http://localhost:3001/api/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    })

    console.log('📥 Response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json()
      console.log('❌ Submission failed:', errorData.error)
      return
    }

    const result = await response.json()

    if (result.success) {
      console.log('✅ Submission successful!')
      console.log('')
      console.log('📊 Response Data:')
      console.log('Message:', result.message)
      if (result.data) {
        console.log('Submission ID:', result.data.id)
        console.log('Status:', result.data.status)
        console.log('Created At:', result.data.created_at)
      }
    } else {
      console.log('❌ Submission failed:', result.error || 'Unknown error')
    }
  } catch (error) {
    console.error('💥 Error:', error.message)
  }
}

testSubmission()
