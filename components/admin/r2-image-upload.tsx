"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ImageIcon, UploadIcon, XIcon } from "lucide-react"
import { toast } from "sonner"
import Image from "next/image"

interface R2ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  label: string
  folder?: string
}

export default function R2ImageUpload({
  value,
  onChange,
  label,
  folder = "uploads"
}: R2ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [preview, setPreview] = useState<string | null>(null)

  useEffect(() => {
    if (value) {
      setPreview(value)
    }
  }, [value])

  const uploadImage = async (file: File) => {
    try {
      setIsUploading(true)

      // 创建表单数据
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', folder)

      // 上传到 R2
      const response = await fetch('/api/upload/r2', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()

      // 设置 URL
      onChange(result.url)
      setPreview(result.url)
      toast.success("Image uploaded successfully to R2")

    } catch (error: any) {
      console.error("Error uploading image:", error)
      toast.error(error.message || "Error uploading image")
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return
    }

    const file = e.target.files[0]

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // 检查文件大小 (最大 1MB)
    if (file.size > 1 * 1024 * 1024) {
      toast.error("Image size should be less than 1MB")
      return
    }

    uploadImage(file)
  }

  const handleRemove = () => {
    onChange("")
    setPreview(null)
    toast.success("Image removed")
  }

  return (
    <div className="space-y-4">
      <Label>{label}</Label>

      {preview ? (
        <div className="relative aspect-video overflow-hidden rounded-md border border-border">
          <Image
            src={preview}
            alt={label}
            fill
            className="object-cover"
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={handleRemove}
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center gap-4 rounded-md border border-dashed border-border p-10">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <ImageIcon className="h-5 w-5 text-primary" />
          </div>
          <div className="text-center">
            <p className="text-sm font-medium">Drag and drop or click to upload</p>
            <p className="text-xs text-muted-foreground">
              SVG, PNG, JPG or GIF (max. 1MB)
            </p>
            <p className="text-xs text-blue-600 mt-1">
              ⚡ Powered by Cloudflare R2
            </p>
          </div>
          <Button
            variant="secondary"
            disabled={isUploading}
            className="relative"
          >
            {isUploading ? "Uploading to R2..." : (
              <>
                <UploadIcon className="mr-2 h-4 w-4" />
                Upload Image
              </>
            )}
            <Input
              type="file"
              accept="image/*"
              className="absolute inset-0 cursor-pointer opacity-0"
              onChange={handleFileChange}
              disabled={isUploading}
            />
          </Button>
        </div>
      )}
    </div>
  )
}
