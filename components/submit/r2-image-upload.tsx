"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ImageIcon, UploadIcon, XIcon, LinkIcon } from "lucide-react"
import { toast } from "sonner"
import Image from "next/image"

interface SubmitImageUploadProps {
  value?: string
  onChange: (url: string) => void
  label: string
  folder?: string
  placeholder?: string
  description?: string
  previewType?: 'logo' | 'cover'  // 新增：预览类型
}

export default function SubmitImageUpload({
  value,
  onChange,
  label,
  folder = "uploads",
  placeholder,
  description,
  previewType = 'cover'
}: SubmitImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [preview, setPreview] = useState<string | null>(null)
  const [showUrlInput, setShowUrlInput] = useState(false)
  const [urlInput, setUrlInput] = useState("")

  useEffect(() => {
    if (value) {
      setPreview(value)
      setUrlInput(value)
    }
  }, [value])

  const uploadImage = async (file: File) => {
    try {
      setIsUploading(true)

      // 创建表单数据
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', folder)

      // 上传到 R2
      const response = await fetch('/api/upload/r2', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()

      // 设置 URL
      onChange(result.url)
      setPreview(result.url)
      setUrlInput(result.url)
      toast.success("Image uploaded successfully!")

    } catch (error: any) {
      console.error("Error uploading image:", error)
      toast.error(error.message || "Error uploading image")
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return
    }

    const file = e.target.files[0]

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // 检查文件大小 (最大 1MB)
    if (file.size > 1 * 1024 * 1024) {
      toast.error("Image size should be less than 1MB")
      return
    }

    uploadImage(file)
  }

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      onChange(urlInput.trim())
      setPreview(urlInput.trim())
      setShowUrlInput(false)
      toast.success("Image URL added")
    }
  }

  const handleRemove = () => {
    onChange("")
    setPreview(null)
    setUrlInput("")
    setShowUrlInput(false)
    toast.success("Image removed")
  }

  // 根据预览类型确定尺寸
  const getPreviewSize = () => {
    if (previewType === 'logo') {
      return {
        container: "w-16 h-16", // 64x64px
        aspect: "aspect-square"
      }
    } else {
      return {
        container: "w-64 h-36", // 256x144px (16:9 比例)
        aspect: "aspect-video"
      }
    }
  }

  const previewSize = getPreviewSize()

  return (
    <div className="space-y-4">
      <Label>{label}</Label>

      {/* 预览区域 - 始终显示 */}
      <div className="space-y-3">
        <div className={`relative ${previewSize.container} ${previewSize.aspect} overflow-hidden rounded-md border border-border bg-gray-50 mx-auto`}>
          {preview ? (
            <>
              <Image
                src={preview}
                alt={label}
                fill
                className="object-cover"
                onError={() => {
                  setPreview(null)
                  toast.error("Failed to load image")
                }}
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6 rounded-full"
                onClick={handleRemove}
              >
                <XIcon className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <ImageIcon className="h-8 w-8 mb-2" />
              <p className="text-xs text-center">No image</p>
            </div>
          )}
        </div>

        {preview && (
          <p className="text-xs text-green-600 text-center">✅ Image ready</p>
        )}
      </div>

      {/* 上传区域 - 分开显示 */}
      <div className="space-y-3">
        {!showUrlInput ? (
          <div className="flex flex-col items-center justify-center gap-3 rounded-md border border-dashed border-border p-6 bg-gray-50/50">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-900">Upload an image</p>
              <p className="text-xs text-gray-500 mt-1">
                PNG, JPG, SVG or GIF (max. 1MB)
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="default"
                size="sm"
                disabled={isUploading}
                className="relative"
              >
                {isUploading ? (
                  "Uploading..."
                ) : (
                  <>
                    <UploadIcon className="mr-2 h-4 w-4" />
                    Choose File
                  </>
                )}
                <Input
                  type="file"
                  accept="image/*"
                  className="absolute inset-0 cursor-pointer opacity-0"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUrlInput(true)}
              >
                <LinkIcon className="mr-2 h-4 w-4" />
                Use URL
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3 p-4 border border-dashed border-border rounded-md bg-gray-50/50">
            <div className="flex items-center gap-2">
              <LinkIcon className="h-4 w-4 text-gray-500" />
              <Label className="text-sm">Image URL</Label>
            </div>
            <Input
              type="url"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder={placeholder || "https://example.com/image.png"}
              className="bg-white"
            />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleUrlSubmit}
                disabled={!urlInput.trim()}
              >
                Add URL
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setShowUrlInput(false)
                  setUrlInput(value || "")
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>

      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
    </div>
  )
}
