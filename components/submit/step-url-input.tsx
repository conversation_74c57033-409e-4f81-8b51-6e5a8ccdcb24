"use client"

import { useState } from "react"
import { Globe, Loader2, Edit3 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { validateUrl, parseProductFromUrl, extractDomainFromUrl, type ParsedProductInfo } from "@/lib/submit/url-parser"

interface StepUrlInputProps {
  url: string
  onUrlChange: (url: string) => void
  onProductInfoParsed: (info: ParsedProductInfo) => void
  onNext: () => void
  onSkipToManual: () => void
}

export function StepUrlInput({ url, onUrlChange, onProductInfoParsed, onNext, onSkipToManual }: StepUrlInputProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleUrlChange = (value: string) => {
    setError("")
    onUrlChange(value)
  }

  const handleParseUrl = async () => {
    if (!url.trim()) {
      setError("Please enter a URL")
      return
    }

    if (!validateUrl(url)) {
      setError("Please enter a valid URL")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      console.log("Starting to parse URL:", url)
      const productInfo = await parseProductFromUrl(url)
      console.log("Parsed product info:", productInfo)
      onProductInfoParsed(productInfo)
      onNext()
    } catch (error) {
      console.error("Parse error:", error)
      setError("Failed to parse product information from URL")
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleParseUrl()
    }
  }

  const domain = url ? extractDomainFromUrl(url) : ""

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Globe className="w-5 h-5" />
          Enter Your Product URL
        </CardTitle>
        <CardDescription>
          Start by entering your product's website URL to automatically extract information, or choose to enter details manually.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="url">Product Website URL</Label>
          <Input
            id="url"
            type="url"
            placeholder="https://yourproduct.com"
            value={url}
            onChange={(e) => handleUrlChange(e.target.value)}
            onKeyPress={handleKeyPress}
            className={error ? "border-red-500" : ""}
          />
          {error && (
            <p className="text-sm text-red-500">{error}</p>
          )}
          {domain && !error && (
            <p className="text-sm text-green-600">✓ Domain: {domain}</p>
          )}
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Choose your preferred method:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>URL Analysis:</strong> We'll automatically extract your product information</li>
            <li>• <strong>Manual Entry:</strong> Enter all details yourself for full control</li>
            <li>• You can always edit and improve the information in the next step</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Button
            onClick={handleParseUrl}
            disabled={!url.trim() || isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Analyzing URL...
              </>
            ) : (
              "Continue with URL"
            )}
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or</span>
            </div>
          </div>

          <Button
            onClick={onSkipToManual}
            variant="outline"
            className="w-full"
            size="lg"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            Enter Details Manually
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
