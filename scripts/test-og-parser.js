#!/usr/bin/env node

/**
 * 测试 Open Graph Scraper + OpenRouter 解析器
 * 
 * 使用方法：
 * node scripts/test-og-parser.js https://example.com
 */

const { OgOpenRouterParser } = require('../lib/parsers/og-openrouter-parser')

async function testParser() {
  const url = process.argv[2] || 'https://vercel.com'
  
  console.log('🚀 Testing OG + OpenRouter Parser')
  console.log('URL:', url)
  console.log('=' .repeat(50))

  const parser = new OgOpenRouterParser()
  
  if (!parser.isAvailable()) {
    console.log('❌ OpenRouter API key not configured')
    console.log('Please set OPENROUTER_API_KEY in your .env.local file')
    console.log('You can get a free API key from: https://openrouter.ai/')
    return
  }

  try {
    const result = await parser.parse(url)
    
    if (result.success) {
      console.log('✅ Parsing successful!')
      console.log('Duration:', result.duration + 'ms')
      console.log('')
      console.log('📊 Extracted Information:')
      console.log('Name:', result.data.name)
      console.log('Tagline:', result.data.tagline)
      console.log('Description:', result.data.description)
      console.log('Logo URL:', result.data.logoUrl)
      console.log('Cover Image URL:', result.data.coverImageUrl)
    } else {
      console.log('❌ Parsing failed:', result.error)
    }
  } catch (error) {
    console.error('💥 Error:', error.message)
  }
}

testParser()
