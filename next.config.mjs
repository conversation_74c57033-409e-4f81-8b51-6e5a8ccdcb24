/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: [
      'pub-5b151ccdf8cd440da7fdcb9ac98c7e70.r2.dev', // 现有的 R2 域名
      '*.r2.dev', // 支持所有 R2 域名
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.r2.dev',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'pub-5b151ccdf8cd440da7fdcb9ac98c7e70.r2.dev',
        port: '',
        pathname: '/**',
      },
    ],
    // 临时禁用图像优化以解决本地开发环境中的连接问题
    unoptimized: true,
    // 以下配置在生产环境中可以重新启用
    // formats: ['image/webp', 'image/avif'],
    // minimumCacheTTL: 60,
  },
  // 移除 experimental.ppr 配置
}

export default nextConfig
