import type { ProductParser, ParseResult, ParsedProductInfo } from './types'
import ogs from 'open-graph-scraper'
import OpenAI from 'openai'

export class OgOpenRouterParser implements ProductParser {
  name = 'og-openrouter'
  private openai: OpenAI | null = null

  constructor() {
    // 初始化 OpenAI 客户端，使用 OpenRouter 作为基础 URL
    if (process.env.OPENROUTER_API_KEY) {
      this.openai = new OpenAI({
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      })
    }
  }

  isAvailable(): boolean {
    return !!process.env.OPENROUTER_API_KEY
  }

  async parse(url: string): Promise<ParseResult> {
    const startTime = Date.now()

    try {
      if (!this.isAvailable()) {
        throw new Error('OpenRouter API key not configured')
      }

      // 第一步：使用 Open Graph Scraper 提取基础信息
      console.log('Step 1: Extracting Open Graph data...')
      const ogData = await this.extractOpenGraphData(url)

      // 第二步：使用 OpenRouter 免费模型进行智能分析和优化
      console.log('Step 2: Enhancing with AI...')
      const enhancedData = await this.enhanceWithAI(ogData, url)

      const duration = Date.now() - startTime
      console.log(`Successfully parsed with OG + OpenRouter in ${duration}ms`)

      return {
        success: true,
        data: enhancedData,
        source: this.name,
        duration
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  /**
   * 使用 Open Graph Scraper 提取基础的结构化数据
   */
  private async extractOpenGraphData(url: string): Promise<any> {
    try {
      const { result } = await ogs({
        url: url,
        timeout: 10000,
        retry: 2,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ProductParser/1.0; +https://introducing.day)'
        }
      })

      return {
        title: result.ogTitle || result.twitterTitle || result.dcTitle,
        description: result.ogDescription || result.twitterDescription || result.dcDescription,
        siteName: result.ogSiteName,
        type: result.ogType,
        url: result.ogUrl || result.requestUrl,
        image: result.ogImage?.[0]?.url || result.twitterImage?.[0]?.url,
        favicon: result.favicon,
        // 额外的有用信息
        author: result.author,
        publishedTime: result.articlePublishedTime,
        modifiedTime: result.articleModifiedTime,
        tags: result.articleTag,
        // 原始数据，供 AI 分析使用
        rawData: result
      }
    } catch (error) {
      console.warn('Open Graph extraction failed:', error)
      // 返回基础信息，让 AI 尝试从 URL 推断
      return {
        url: url,
        title: null,
        description: null,
        image: null,
        rawData: null
      }
    }
  }

  /**
   * 使用 OpenRouter 免费模型对提取的信息进行智能分析和优化
   */
  private async enhanceWithAI(ogData: any, url: string): Promise<ParsedProductInfo> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized')
    }

    try {
      const prompt = this.buildAnalysisPrompt(ogData, url)

      const completion = await this.openai.chat.completions.create({
        model: 'meta-llama/llama-3.2-3b-instruct:free', // 使用免费的 Llama 模型
        messages: [
          {
            role: 'system',
            content: 'You are a product information extraction expert. Analyze the provided data and extract clean, accurate product information. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      })

      const response = completion.choices[0]?.message?.content
      if (!response) {
        throw new Error('No response from AI model')
      }

      // 清理并解析 AI 返回的 JSON
      const cleanedResponse = this.cleanJsonResponse(response)
      const aiResult = JSON.parse(cleanedResponse)

      // 构建最终结果，结合 OG 数据和 AI 分析
      return this.buildFinalResult(ogData, aiResult, url)

    } catch (error) {
      console.warn('AI enhancement failed, falling back to OG data:', error)
      // 如果 AI 分析失败，回退到基础的 OG 数据处理
      return this.buildFallbackResult(ogData, url)
    }
  }

  /**
   * 清理 AI 返回的响应，移除 markdown 代码块标记
   */
  private cleanJsonResponse(response: string): string {
    // 移除 markdown 代码块标记
    let cleaned = response.trim()

    // 移除开头的 ```json 或 ```
    cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '')

    // 移除结尾的 ```
    cleaned = cleaned.replace(/\n?\s*```\s*$/i, '')

    return cleaned.trim()
  }

  /**
   * 构建给 AI 分析的提示词
   */
  private buildAnalysisPrompt(ogData: any, url: string): string {
    return `
Analyze this website data and extract product information. Return ONLY a JSON object with these exact fields:

URL: ${url}
Title: ${ogData.title || 'N/A'}
Description: ${ogData.description || 'N/A'}
Site Name: ${ogData.siteName || 'N/A'}
Type: ${ogData.type || 'N/A'}

Extract and return:
{
  "name": "Clean product/company name (remove taglines, descriptions)",
  "tagline": "Short, compelling tagline (max 100 chars)",
  "description": "Clear description of what the product does (max 300 chars)",
  "category": "Product category (e.g., 'AI Tool', 'SaaS', 'Mobile App')",
  "confidence": "High/Medium/Low - your confidence in the extraction"
}

Rules:
- Name should be just the product/company name, not a sentence
- Tagline should be catchy and descriptive
- Description should explain what the product does
- If data is unclear, use "Unknown" for that field
- Return valid JSON only, no explanations
`.trim()
  }

  /**
   * 构建最终结果，结合 OG 数据和 AI 分析
   */
  private buildFinalResult(ogData: any, aiResult: any, url: string): ParsedProductInfo {
    return {
      name: aiResult.name || this.cleanTitle(ogData.title) || this.generateNameFromUrl(url),
      tagline: aiResult.tagline || this.extractTaglineFromDescription(ogData.description),
      description: aiResult.description || ogData.description || 'No description available',
      logoUrl: this.extractFavicon(ogData.favicon, url),
      coverImageUrl: this.normalizeImageUrl(ogData.image, url)
    }
  }

  /**
   * 回退结果构建（当 AI 分析失败时）
   */
  private buildFallbackResult(ogData: any, url: string): ParsedProductInfo {
    return {
      name: this.cleanTitle(ogData.title) || this.generateNameFromUrl(url),
      tagline: this.extractTaglineFromDescription(ogData.description),
      description: ogData.description || 'No description available',
      logoUrl: this.extractFavicon(ogData.favicon, url),
      coverImageUrl: this.normalizeImageUrl(ogData.image, url)
    }
  }

  /**
   * 清理标题，移除常见的后缀
   */
  private cleanTitle(title: string | null): string | undefined {
    if (!title) return undefined

    let cleaned = title.trim()

    // 移除常见的分隔符后的内容
    cleaned = cleaned.replace(/\s*[\|\-–—·]\s*.+$/, '')
    cleaned = cleaned.replace(/\s*:\s*.+$/, '')

    return cleaned.length > 1 ? cleaned : undefined
  }

  /**
   * 从描述中提取标语
   */
  private extractTaglineFromDescription(description: string | null): string | undefined {
    if (!description) return undefined

    // 如果描述较短，直接使用
    if (description.length <= 120) {
      return description
    }

    // 提取第一句话
    const firstSentence = description.split(/[.!?]/)[0].trim()
    if (firstSentence.length > 10 && firstSentence.length <= 120) {
      return firstSentence
    }

    // 截取前120个字符
    return description.substring(0, 117) + '...'
  }

  /**
   * 处理 favicon URL
   */
  private extractFavicon(favicon: string | null, url: string): string | undefined {
    if (favicon) {
      return this.normalizeImageUrl(favicon, url)
    }

    // 回退到默认 favicon
    try {
      const baseUrl = new URL(url).origin
      return `${baseUrl}/favicon.ico`
    } catch {
      return undefined
    }
  }

  /**
   * 标准化图片 URL
   */
  private normalizeImageUrl(imageUrl: string | null, baseUrl: string): string | undefined {
    if (!imageUrl) return undefined

    try {
      // 如果已经是完整 URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 处理协议相对 URL
      if (imageUrl.startsWith('//')) {
        return `https:${imageUrl}`
      }

      // 处理绝对路径
      const base = new URL(baseUrl).origin
      if (imageUrl.startsWith('/')) {
        return `${base}${imageUrl}`
      }

      // 处理相对路径
      return `${base}/${imageUrl}`
    } catch {
      return undefined
    }
  }

  /**
   * 从 URL 生成产品名称
   */
  private generateNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
      const domain = urlObj.hostname.replace('www.', '')
      const domainParts = domain.split('.')
      return domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    } catch {
      return 'Unknown Product'
    }
  }
}
