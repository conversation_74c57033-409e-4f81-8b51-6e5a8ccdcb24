import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient, createSupabaseAdminClient } from "@/lib/supabase/server"

interface SubmissionData {
  url: string
  name: string
  tagline: string
  description: string
  logoUrl?: string
  coverImageUrl?: string
  category: string
  tags: string[]
  preferredDate?: string
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Check if user is authenticated (required for submissions)
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: "Authentication required. Please sign in to submit a product." }, { status: 401 })
    }

    const body: SubmissionData = await request.json()

    // Validate required fields
    if (!body.url || !body.name || !body.tagline || !body.description || !body.category) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Prepare data for submissions table
    const submissionData = {
      name: body.name.trim(),
      tagline: body.tagline.trim(),
      description: body.description.trim(),
      url: body.url.trim(),
      logo_url: body.logoUrl || null,
      cover_image_url: body.coverImageUrl || null,
      category: body.category.trim(),
      tags: Array.isArray(body.tags) ? body.tags : [],
      preferred_date: body.preferredDate ? new Date(body.preferredDate).toISOString() : null,
      submitted_by: user?.id || null,
      submitter_email: user?.email || null,
      submitter_name: user?.user_metadata?.full_name || user?.user_metadata?.name || null,
      status: 'pending'
    }

    // Insert into submissions table
    const { data, error } = await supabase
      .from("submissions")
      .insert([submissionData])
      .select()
      .single()

    if (error) {
      console.error("Error inserting submission:", error)
      return NextResponse.json({ error: "Failed to submit product" }, { status: 500 })
    }

    console.log('Product submission saved:', data)

    const response = {
      success: true,
      message: "Product submitted successfully! We'll review it and get back to you soon.",
      data: {
        id: data.id,
        status: data.status,
        created_at: data.created_at
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in submit API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
