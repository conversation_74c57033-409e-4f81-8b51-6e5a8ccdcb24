import { NextRequest, NextResponse } from 'next/server'
import { uploadToR2, generateUniqueFileName, validateR2Config } from '@/lib/cloudflare/r2-client'

export async function POST(request: NextRequest) {
  try {
    // 验证 R2 配置
    if (!validateR2Config()) {
      return NextResponse.json(
        { error: 'R2 configuration is incomplete' },
        { status: 500 }
      )
    }

    // 获取表单数据
    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string || 'uploads'

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      )
    }

    // 验证文件大小 (最大 1MB)
    const maxSize = 1 * 1024 * 1024 // 1MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size must be less than 1MB' },
        { status: 400 }
      )
    }

    // 生成唯一文件名
    const uniqueFileName = generateUniqueFileName(file.name, folder)

    // 将文件转换为 Buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // 上传到 R2
    const publicUrl = await uploadToR2(buffer, uniqueFileName, file.type)

    return NextResponse.json({
      success: true,
      url: publicUrl,
      key: uniqueFileName,
      size: file.size,
      type: file.type,
    })

  } catch (error) {
    console.error('Error uploading to R2:', error)
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}

// 处理预签名 URL 请求
export async function GET(request: NextRequest) {
  try {
    // 验证 R2 配置
    if (!validateR2Config()) {
      return NextResponse.json(
        { error: 'R2 configuration is incomplete' },
        { status: 500 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileName = searchParams.get('fileName')
    const contentType = searchParams.get('contentType')
    const folder = searchParams.get('folder') || 'uploads'

    if (!fileName || !contentType) {
      return NextResponse.json(
        { error: 'fileName and contentType are required' },
        { status: 400 }
      )
    }

    // 验证内容类型
    if (!contentType.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      )
    }

    // 生成唯一文件名
    const uniqueFileName = generateUniqueFileName(fileName, folder)

    // 生成预签名 URL（这里暂时返回错误，因为客户端直接上传需要 CORS 配置）
    return NextResponse.json(
      { error: 'Presigned uploads not yet implemented. Use POST method instead.' },
      { status: 501 }
    )

  } catch (error) {
    console.error('Error generating presigned URL:', error)
    return NextResponse.json(
      { error: 'Failed to generate presigned URL' },
      { status: 500 }
    )
  }
}
