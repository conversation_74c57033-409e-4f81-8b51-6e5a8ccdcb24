import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Get user's submissions
    const { data: submissions, error } = await supabase
      .from('submissions')
      .select('*')
      .eq('submitted_by', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching submissions:', error)
      return NextResponse.json({ error: 'Failed to fetch submissions' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      data: submissions 
    })
  } catch (error) {
    console.error('Error in submissions API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
